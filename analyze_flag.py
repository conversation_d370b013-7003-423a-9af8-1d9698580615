#!/usr/bin/env python3

# 解密得到的字符
decrypted_chars = "èÄåËçÄìØàê"
decrypted_bytes = [ord(c) for c in decrypted_chars]

print("解密得到的字符分析:")
print(f"字符串: {decrypted_chars}")
print(f"字节值: {[hex(b) for b in decrypted_bytes]}")
print(f"字节值: {decrypted_bytes}")

# 尝试不同的解释方式
print("\n尝试不同的编码解释:")

# 1. 尝试将这些字节作为ASCII字符的某种变换
print("1. 检查是否是ASCII字符的简单变换:")
for offset in [-128, -64, -32, -16, -8, 8, 16, 32, 64, 128]:
    result = ""
    valid = True
    for b in decrypted_bytes:
        new_val = (b + offset) % 256
        if 32 <= new_val <= 126:  # 可打印ASCII
            result += chr(new_val)
        else:
            valid = False
            break
    if valid and len(result) == len(decrypted_chars):
        print(f"  偏移 {offset}: {result}")

# 2. 尝试位操作
print("\n2. 尝试位操作变换:")
for xor_val in [0x80, 0x40, 0x20, 0x10, 0x08, 0x04, 0x02, 0x01]:
    result = ""
    valid = True
    for b in decrypted_bytes:
        new_val = b ^ xor_val
        if 32 <= new_val <= 126:
            result += chr(new_val)
        else:
            valid = False
            break
    if valid:
        print(f"  异或 0x{xor_val:02x}: {result}")

# 3. 尝试组合操作
print("\n3. 尝试组合操作 (先异或再偏移):")
for xor_val in [0x80, 0x40, 0x20, 0x10]:
    for offset in [-32, -16, -8, 8, 16, 32]:
        result = ""
        valid = True
        for b in decrypted_bytes:
            new_val = (b ^ xor_val) + offset
            if 32 <= new_val <= 126:
                result += chr(new_val)
            else:
                valid = False
                break
        if valid and len(result) == len(decrypted_chars):
            print(f"  异或 0x{xor_val:02x} + 偏移 {offset}: {result}")

# 4. 检查是否是flag格式
print("\n4. 寻找可能的flag格式:")
# 常见的flag格式: flag{...}, FLAG{...}, ctf{...}, 等
flag_prefixes = ['flag{', 'FLAG{', 'ctf{', 'CTF{']
for prefix in flag_prefixes:
    if len(decrypted_chars) >= len(prefix):
        # 尝试匹配前缀
        for xor_val in range(256):
            candidate = ""
            valid = True
            for i, b in enumerate(decrypted_bytes):
                new_val = b ^ xor_val
                if 32 <= new_val <= 126:
                    candidate += chr(new_val)
                else:
                    valid = False
                    break
            if valid and candidate.startswith(prefix.lower()):
                print(f"  可能的flag (异或 0x{xor_val:02x}): {candidate}")

print(f"\n原始解密结果: {decrypted_chars}")
print("如果以上没有找到合理的flag格式，原始结果可能就是答案，或者需要进一步分析。")
