#!/usr/bin/env python3

# 从IDA中提取的加密数据
encrypted_data = [0xc7, 0xeb, 0xca, 0xe4, 0xc8, 0xeb, 0xc3, 0xf7, 0xce, 0xc4]

def encrypt_char(original_char, position):
    """根据汇编代码实现的加密函数"""
    original_byte = ord(original_char)
    # 步骤1: xor dl, 0Fh
    step1 = original_byte ^ 0x0F
    # 步骤2: sub dl, al (al = position // 8)
    step2 = (step1 - (position // 8)) & 0xFF  # 字节级减法，自动处理溢出
    # 步骤3: xor dl, 20h
    step3 = step2 ^ 0x20
    return step3

def decrypt_char(encrypted_byte, position):
    """逆向解密函数"""
    # 逆向步骤3: xor dl, 20h
    step1 = encrypted_byte ^ 0x20
    # 逆向步骤2: sub dl, al -> add dl, al
    step2 = (step1 + (position // 8)) & 0xFF
    # 逆向步骤1: xor dl, 0Fh
    original = step2 ^ 0x0F
    return original

def brute_force_decrypt():
    """暴力破解验证"""
    result = []
    for i, encrypted_byte in enumerate(encrypted_data):
        found = False
        for ascii_val in range(32, 127):  # 可打印ASCII字符
            if encrypt_char(chr(ascii_val), i) == encrypted_byte:
                result.append(chr(ascii_val))
                print(f"位置{i}: 找到字符 '{chr(ascii_val)}' (0x{ascii_val:02x}) -> 0x{encrypted_byte:02x}")
                found = True
                break
        
        if not found:
            # 尝试直接解密
            decrypted_val = decrypt_char(encrypted_byte, i)
            if 32 <= decrypted_val <= 126:
                result.append(chr(decrypted_val))
                print(f"位置{i}: 直接解密得到 '{chr(decrypted_val)}' (0x{decrypted_val:02x}) -> 0x{encrypted_byte:02x}")
            else:
                result.append('?')
                print(f"位置{i}: 无法解密 (0x{encrypted_byte:02x}) -> 不可打印字符 (0x{decrypted_val:02x})")
    
    return ''.join(result)

def direct_decrypt():
    """直接解密方法"""
    result = []
    for i, encrypted_byte in enumerate(encrypted_data):
        decrypted_val = decrypt_char(encrypted_byte, i)
        result.append(chr(decrypted_val))
        print(f"位置{i}: 0x{encrypted_byte:02x} -> '{chr(decrypted_val)}' (0x{decrypted_val:02x})")
    return ''.join(result)

print("方法1: 直接解密")
flag1 = direct_decrypt()
print(f"解密结果: {flag1}")

print("\n方法2: 暴力破解验证")
flag2 = brute_force_decrypt()
print(f"解密结果: {flag2}")

# 验证加密过程
print(f"\n验证加密过程 (使用flag1: {flag1}):")
for i, char in enumerate(flag1):
    encrypted = encrypt_char(char, i)
    expected = encrypted_data[i]
    match = "✓" if encrypted == expected else "✗"
    print(f"位置{i}: '{char}' (0x{ord(char):02x}) -> 0x{encrypted:02x} (期望: 0x{expected:02x}) {match}")

print(f"\n最终flag: {flag1}")
