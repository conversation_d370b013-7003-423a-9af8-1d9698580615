#!/usr/bin/env python3

# 从IDA中提取的加密数据
encrypted_data = [0xc7, 0xeb, 0xca, 0xe4, 0xc8, 0xeb, 0xc3, 0xf7, 0xce, 0xc4]

def simulate_encryption(original_text):
    """模拟原始的加密过程"""
    result = []
    for i, char in enumerate(original_text):
        original_byte = ord(char)
        # 加密算法: ((original ^ 0xF) - i // 8) ^ 0x20
        step1 = original_byte ^ 0xF
        step2 = step1 - (i // 8)
        # 处理负数情况 - 在C语言中，char是有符号的
        if step2 < 0:
            step2 = (step2 + 256) % 256
        elif step2 > 255:
            step2 = step2 % 256
        step3 = step2 ^ 0x20
        result.append(step3)
    return result

def decrypt_char(encrypted_byte, position):
    """
    逆向加密算法：
    原始加密: encrypted[i] = ((original[i] ^ 0xF) - i / 8) ^ 0x20
    逆向解密: original[i] = ((encrypted[i] ^ 0x20) + i / 8) ^ 0xF
    """
    # 先异或0x20
    step1 = encrypted_byte ^ 0x20
    # 加上 i // 8 (考虑到可能的溢出)
    step2 = (step1 + position // 8) % 256
    # 最后异或0xF
    original = step2 ^ 0xF
    return original

def try_different_interpretations():
    """尝试不同的解释方式"""
    print("尝试不同的解密方法:")

    # 方法1: 标准解密
    print("\n方法1 - 标准解密:")
    for i, encrypted_byte in enumerate(encrypted_data):
        step1 = encrypted_byte ^ 0x20
        step2 = (step1 + i // 8) % 256
        original = step2 ^ 0xF
        print(f"位置{i}: 0x{encrypted_byte:02x} -> '{chr(original)}' (0x{original:02x})")

    # 方法2: 考虑有符号运算
    print("\n方法2 - 考虑有符号运算:")
    for i, encrypted_byte in enumerate(encrypted_data):
        step1 = encrypted_byte ^ 0x20
        # 将step1视为有符号数
        if step1 > 127:
            step1 = step1 - 256
        step2 = (step1 + i // 8) % 256
        if step2 < 0:
            step2 = step2 + 256
        original = step2 ^ 0xF
        if 32 <= original <= 126:  # 可打印ASCII字符
            print(f"位置{i}: 0x{encrypted_byte:02x} -> '{chr(original)}' (0x{original:02x})")
        else:
            print(f"位置{i}: 0x{encrypted_byte:02x} -> [不可打印] (0x{original:02x})")

    # 方法3: 尝试不同的运算顺序
    print("\n方法3 - 不同运算顺序:")
    for i, encrypted_byte in enumerate(encrypted_data):
        # 尝试: original = (encrypted ^ 0x20 ^ 0xF) + i // 8
        original = ((encrypted_byte ^ 0x20 ^ 0xF) + i // 8) % 256
        if 32 <= original <= 126:
            print(f"位置{i}: 0x{encrypted_byte:02x} -> '{chr(original)}' (0x{original:02x})")
        else:
            print(f"位置{i}: 0x{encrypted_byte:02x} -> [不可打印] (0x{original:02x})")

def decrypt_flag():
    decrypted = []
    for i, encrypted_byte in enumerate(encrypted_data):
        original_char = decrypt_char(encrypted_byte, i)
        decrypted.append(chr(original_char))
    
    return ''.join(decrypted)

def brute_force_decrypt():
    """暴力破解每个位置的字符"""
    result = []
    for i, encrypted_byte in enumerate(encrypted_data):
        found = False
        for ascii_val in range(32, 127):  # 可打印ASCII字符范围
            # 模拟加密过程
            encrypted_test = simulate_encryption(chr(ascii_val))[0]
            # 调整位置影响
            step1 = ascii_val ^ 0xF
            step2 = step1 - (i // 8)
            if step2 < 0:
                step2 = (step2 + 256) % 256
            elif step2 > 255:
                step2 = step2 % 256
            step3 = step2 ^ 0x20

            if step3 == encrypted_byte:
                result.append(chr(ascii_val))
                print(f"位置{i}: 找到字符 '{chr(ascii_val)}' (0x{ascii_val:02x}) -> 0x{encrypted_byte:02x}")
                found = True
                break

        if not found:
            print(f"位置{i}: 未找到匹配的可打印字符 (加密值: 0x{encrypted_byte:02x})")
            result.append('?')

    return ''.join(result)

if __name__ == "__main__":
    print("暴力破解解密:")
    flag = brute_force_decrypt()
    print(f"\n解密后的flag: {flag}")

    # 验证完整字符串
    print(f"\n验证完整字符串加密:")
    encrypted_result = simulate_encryption(flag)
    print(f"原始数据: {[hex(x) for x in encrypted_data]}")
    print(f"加密结果: {[hex(x) for x in encrypted_result]}")
    print(f"匹配: {encrypted_result == encrypted_data}")
