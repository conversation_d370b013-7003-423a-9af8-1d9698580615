#!/usr/bin/env python3

# 解密得到的字符
decrypted_chars = "èÄåËçÄìØàê"
decrypted_bytes = [ord(c) for c in decrypted_chars]

# 应用异或0x80变换
transformed = ""
for b in decrypted_bytes:
    transformed += chr(b ^ 0x80)

print(f"原始解密结果: {decrypted_chars}")
print(f"异或0x80后: {transformed}")

# 分析transformed结果
print(f"\n分析 '{transformed}':")
print("字符分析:")
for i, char in enumerate(transformed):
    print(f"  位置{i}: '{char}' (ASCII: {ord(char)}, 0x{ord(char):02x})")

# 检查是否包含常见的flag模式
common_patterns = ['flag', 'FLAG', 'ctf', 'CTF', 'key', 'KEY']
print(f"\n检查常见模式:")
for pattern in common_patterns:
    if pattern.lower() in transformed.lower():
        print(f"  找到模式: {pattern}")

# 尝试不同的大小写组合
print(f"\n可能的flag变体:")
print(f"  小写: {transformed.lower()}")
print(f"  大写: {transformed.upper()}")
print(f"  首字母大写: {transformed.capitalize()}")

# 检查是否需要添加flag格式
if not transformed.lower().startswith('flag'):
    print(f"\n可能需要添加flag格式:")
    print(f"  flag{{{transformed}}}")
    print(f"  flag{{{transformed.lower()}}}")
    print(f"  FLAG{{{transformed.upper()}}}")

# 最终结论
print(f"\n=== 最终分析 ===")
print(f"1. 从程序中提取的加密数据: {[hex(x) for x in [0xc7, 0xeb, 0xca, 0xe4, 0xc8, 0xeb, 0xc3, 0xf7, 0xce, 0xc4]]}")
print(f"2. 应用逆向加密算法得到: {decrypted_chars}")
print(f"3. 异或0x80得到可读字符: {transformed}")
print(f"4. 可能的最终flag: {transformed}")

# 验证完整的解密过程
print(f"\n=== 完整验证 ===")
original_encrypted = [0xc7, 0xeb, 0xca, 0xe4, 0xc8, 0xeb, 0xc3, 0xf7, 0xce, 0xc4]

def full_decrypt_process(encrypted_data):
    """完整的解密过程"""
    # 步骤1: 逆向原始加密算法
    step1_result = []
    for i, encrypted_byte in enumerate(encrypted_data):
        # 逆向: ((original ^ 0xF) - i // 8) ^ 0x20
        temp = encrypted_byte ^ 0x20
        temp = (temp + (i // 8)) & 0xFF
        original = temp ^ 0x0F
        step1_result.append(original)
    
    # 步骤2: 异或0x80得到最终结果
    final_result = ""
    for b in step1_result:
        final_result += chr(b ^ 0x80)
    
    return final_result

final_flag = full_decrypt_process(original_encrypted)
print(f"完整解密流程得到的flag: {final_flag}")
