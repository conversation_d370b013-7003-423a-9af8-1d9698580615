#!/usr/bin/env python3

# 从IDA中提取的加密数据
encrypted_data = [0xc7, 0xeb, 0xca, 0xe4, 0xc8, 0xeb, 0xc3, 0xf7, 0xce, 0xc4]

def test_encryption(original_char, position):
    """测试单个字符的加密过程"""
    original_byte = ord(original_char)
    # 加密算法: ((original ^ 0xF) - i // 8) ^ 0x20
    step1 = original_byte ^ 0xF
    step2 = step1 - (position // 8)
    # 处理字节溢出
    step2 = step2 & 0xFF
    step3 = step2 ^ 0x20
    return step3

def brute_force_position(encrypted_byte, position):
    """暴力破解单个位置的字符"""
    for ascii_val in range(32, 127):  # 可打印ASCII字符
        if test_encryption(chr(ascii_val), position) == encrypted_byte:
            return chr(ascii_val)
    return None

print("开始暴力破解...")
result = []
for i, encrypted_byte in enumerate(encrypted_data):
    char = brute_force_position(encrypted_byte, i)
    if char:
        print(f"位置{i}: 找到字符 '{char}' -> 0x{encrypted_byte:02x}")
        result.append(char)
    else:
        print(f"位置{i}: 未找到匹配字符 (0x{encrypted_byte:02x})")
        result.append('?')

flag = ''.join(result)
print(f"\n解密结果: {flag}")

# 验证
print("\n验证:")
for i, char in enumerate(flag):
    if char != '?':
        encrypted = test_encryption(char, i)
        expected = encrypted_data[i]
        match = "✓" if encrypted == expected else "✗"
        print(f"位置{i}: '{char}' -> 0x{encrypted:02x} (期望: 0x{expected:02x}) {match}")
